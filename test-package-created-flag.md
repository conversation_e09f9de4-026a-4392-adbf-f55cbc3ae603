# Test Package Created Flag Implementation

## Summary of Changes

### Backend Changes

1. **Order Entity** (`tts-be-nestjs/src/orders/entities/order.entity.ts`)
   - Added `packageCreated` boolean field with default `false`

2. **Database Migration** (`tts-be-nestjs/src/migrations/1754818123682-AddPackageCreatedToOrders.ts`)
   - Added column `packageCreated` to `orders` table

3. **Orders Service** (`tts-be-nestjs/src/orders/orders.service.ts`)
   - Updated `createPackage` method to set `packageCreated = true` after successful package creation
   - Updated code 21011006 handling to also set `packageCreated = true` when package already exists

4. **DTOs**
   - Added `packageCreated` field to `OrderResponseDto`
   - Added `packageCreated` field to `CreateOrderDto`
   - `UpdateOrderDto` automatically inherits the field

### Frontend Changes

1. **Order Type** (`tts-fe-nextjs/src/types/order.ts`)
   - Added `packageCreated?: boolean` to Order interface

2. **Order Detail Page** (`tts-fe-nextjs/src/app/(client)/client/tiktok/order/[id]/page.tsx`)
   - Added `useSynchronizeOrderDetails` hook
   - Added `handleSyncOrderDetails` function
   - Updated button logic:
     - **Create Label**: Only show when `!order.packageCreated` and status is `AWAITING_SHIPMENT`
     - **Sync Order Details**: Show when `order.packageCreated` and status is NOT `AWAITING_COLLECTION`
     - **Download Label**: Only show when status is `AWAITING_COLLECTION`

## Logic Flow

1. **Initial State**: Order has `packageCreated = false`
2. **After Create Package**: 
   - If successful → `packageCreated = true`
   - If code 21011006 (already exists) → `packageCreated = true` + sync order details
3. **Button Display Logic**:
   - `packageCreated = false` → Show "Create Label" button
   - `packageCreated = true` + status ≠ `AWAITING_COLLECTION` → Show "Sync Order Details" button
   - `packageCreated = true` + status = `AWAITING_COLLECTION` → Show "Download Label" button

## Testing Steps

1. **Test Create Package Flow**:
   - Find an order with status `AWAITING_SHIPMENT` and `packageCreated = false`
   - Should show "Create Label" button
   - Click "Create Label" → Should create package and set `packageCreated = true`
   - After success, should show "Sync Order Details" button

2. **Test Package Already Exists Flow**:
   - Try to create package for order that already has package in TikTok
   - Should get code 21011006, sync order details, and set `packageCreated = true`
   - Should show "Sync Order Details" button

3. **Test Sync Order Details Flow**:
   - Order with `packageCreated = true` and status ≠ `AWAITING_COLLECTION`
   - Should show "Sync Order Details" button
   - Click should sync order details from TikTok

4. **Test Download Label Flow**:
   - Order with status `AWAITING_COLLECTION`
   - Should show "Download Label" button
   - Click should download shipping label

## Database Migration Status

Migration has been run successfully:
- Added `packageCreated` column to `orders` table
- Default value is `false` for all existing orders

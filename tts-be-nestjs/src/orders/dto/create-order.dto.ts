import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsEnum, IsArray, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus, PaymentInfo, RecipientAddress } from '../entities/order.entity';

export class CreateOrderDto {
  @ApiProperty({
    description: 'Order ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @IsNotEmpty()
  @IsString()
  idTT: string;

  @ApiPropertyOptional({
    description: 'Order status',
    enum: OrderStatus,
    example: OrderStatus.AWAITING_SHIPMENT,
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({
    description: 'Order type',
    example: 'PRE_ORDER',
  })
  @IsOptional()
  @IsString()
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Create time from TikTok Shop (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Update time from TikTok Shop (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeTT?: number;

  @ApiPropertyOptional({
    description: 'Paid time from TikTok Shop (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  paidTime?: number;

  @ApiPropertyOptional({
    description: 'Buyer email',
    example: 'buyer****@example.com',
  })
  @IsOptional()
  @IsString()
  buyerEmail?: string;

  @ApiPropertyOptional({
    description: 'Buyer message',
    example: 'Please deliver after 5 PM',
  })
  @IsOptional()
  @IsString()
  buyerMessage?: string;

  @ApiPropertyOptional({
    description: 'TikTok buyer user ID',
    example: '7123456789',
  })
  @IsOptional()
  @IsString()
  userIdTT?: string;

  @ApiPropertyOptional({
    description: 'Payment information',
    example: {
      totalAmount: '29.99',
      currency: 'USD',
      tax: '2.40',
      shippingFee: '5.99',
    },
  })
  @IsOptional()
  payment?: PaymentInfo;

  @ApiPropertyOptional({
    description: 'Payment method name',
    example: 'Credit Card',
  })
  @IsOptional()
  @IsString()
  paymentMethodName?: string;

  @ApiPropertyOptional({
    description: 'Recipient address',
    example: {
      name: 'John Doe',
      addressLine1: '123 Main St',
      postalCode: '12345',
      regionCode: 'US',
    },
  })
  @IsOptional()
  recipientAddress?: RecipientAddress;

  @ApiPropertyOptional({
    description: 'Fulfillment type',
    example: 'FULFILLMENT_BY_SELLER',
  })
  @IsOptional()
  @IsString()
  fulfillmentType?: string;

  @ApiPropertyOptional({
    description: 'Shipping provider',
    example: 'UPS',
  })
  @IsOptional()
  @IsString()
  shippingProvider?: string;

  @ApiPropertyOptional({
    description: 'Shipping provider ID',
    example: '12345',
  })
  @IsOptional()
  @IsString()
  shippingProviderId?: string;

  @ApiPropertyOptional({
    description: 'Shipping type',
    example: 'TIKTOK',
  })
  @IsOptional()
  @IsString()
  shippingType?: string;

  @ApiPropertyOptional({
    description: 'Tracking number',
    example: '1Z999AA1234567890',
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiPropertyOptional({
    description: 'Delivery option ID',
    example: 'standard_shipping',
  })
  @IsOptional()
  @IsString()
  deliveryOptionId?: string;

  @ApiPropertyOptional({
    description: 'Delivery option name',
    example: 'Standard Shipping',
  })
  @IsOptional()
  @IsString()
  deliveryOptionName?: string;

  @ApiPropertyOptional({
    description: 'Delivery type (HOME_DELIVERY or COLLECTION_POINT)',
    example: 'HOME_DELIVERY',
  })
  @IsOptional()
  @IsString()
  deliveryType?: string;

  @ApiPropertyOptional({
    description: 'Is COD order',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isCod?: boolean;

  @ApiPropertyOptional({
    description: 'Is exchange order',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isExchangeOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Is replacement order',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isReplacementOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Is sample order',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isSampleOrder?: boolean;

  @ApiPropertyOptional({
    description: 'Flag to indicate if package has been created successfully',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  packageCreated?: boolean;

  @ApiPropertyOptional({
    description: 'Packages information from TikTok Shop',
    example: [
      {
        id: '1154528977690660930'
      }
    ],
  })
  @IsOptional()
  @IsArray()
  packages?: { id: string }[];

  @ApiPropertyOptional({
    description: 'Raw TikTok response data',
  })
  @IsOptional()
  rawTikTokResponse?: any;

  @ApiProperty({
    description: 'TikTok Shop ID',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId: number;

  @ApiPropertyOptional({
    description: 'User ID',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'Order line items',
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  lineItems?: CreateOrderLineItemDto[];
}

export class CreateOrderLineItemDto {
  @ApiPropertyOptional({
    description: 'Line item ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @IsOptional()
  @IsString()
  idTT?: string;

  @ApiPropertyOptional({
    description: 'Product ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @IsOptional()
  @IsString()
  productIdTT?: string;

  @ApiPropertyOptional({
    description: 'SKU ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @IsOptional()
  @IsString()
  skuIdTT?: string;

  @ApiPropertyOptional({
    description: 'Seller SKU',
    example: 'TSHIRT-BLK-M',
  })
  @IsOptional()
  @IsString()
  sellerSku?: string;

  @ApiPropertyOptional({
    description: 'Product name',
    example: 'Classic Cotton T-Shirt',
  })
  @IsOptional()
  @IsString()
  productName?: string;

  @ApiPropertyOptional({
    description: 'SKU name',
    example: 'Black, Medium',
  })
  @IsOptional()
  @IsString()
  skuName?: string;

  @ApiPropertyOptional({
    description: 'SKU image URL',
    example: 'https://example.com/image.jpg',
  })
  @IsOptional()
  @IsString()
  skuImage?: string;

  @ApiPropertyOptional({
    description: 'Original price',
    example: 29.99,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  originalPrice?: number;

  @ApiPropertyOptional({
    description: 'Sale price',
    example: 24.99,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  salePrice?: number;

  @ApiPropertyOptional({
    description: 'Currency',
    example: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Platform discount',
    example: 2.00,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  platformDiscount?: number;

  @ApiPropertyOptional({
    description: 'Seller discount',
    example: 3.00,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  sellerDiscount?: number;

  @ApiPropertyOptional({
    description: 'Package ID',
    example: 'PKG123456',
  })
  @IsOptional()
  @IsString()
  packageId?: string;

  @ApiPropertyOptional({
    description: 'Package status',
    example: 'TO_FULFILL',
  })
  @IsOptional()
  @IsString()
  packageStatus?: string;

  @ApiPropertyOptional({
    description: 'Display status',
    example: 'AWAITING_SHIPMENT',
  })
  @IsOptional()
  @IsString()
  displayStatus?: string;

  @ApiPropertyOptional({
    description: 'Is dangerous good',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isDangerousGood?: boolean;

  @ApiPropertyOptional({
    description: 'Is gift',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isGift?: boolean;

  @ApiPropertyOptional({
    description: 'Item tax details',
  })
  @IsOptional()
  itemTax?: any[];

  @ApiPropertyOptional({
    description: 'Combined listing SKUs',
  })
  @IsOptional()
  combinedListingSkus?: any[];

  @ApiPropertyOptional({
    description: 'Quantity',
    example: 2,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  quantity?: number;

  @ApiPropertyOptional({
    description: 'Raw TikTok line item response',
  })
  @IsOptional()
  rawTikTokResponse?: any;
}

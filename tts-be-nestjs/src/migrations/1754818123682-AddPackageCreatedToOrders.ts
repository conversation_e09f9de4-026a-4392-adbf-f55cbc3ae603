import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPackageCreatedToOrders1754818123682 implements MigrationInterface {
    name = 'AddPackageCreatedToOrders1754818123682'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD "packageCreated" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "packageCreated"`);
    }

}
